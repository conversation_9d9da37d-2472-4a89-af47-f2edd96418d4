package com.trinasolar.integration.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.trinasolar.integration.api.dto.SystemSyncReqDTO;
import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.api.entity.*;
import com.trinasolar.integration.api.knowledge.ISpaceProvider;
import com.trinasolar.integration.api.knowledge.SpaceDTO;
import com.trinasolar.integration.constants.ErrorCodeConstants;
import com.trinasolar.integration.constants.GitLabAccessLevel;
import com.trinasolar.integration.constants.RelationTypeConstant;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectRespVO;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectSaveReqVO;
import com.trinasolar.integration.controller.devops.vo.GitGroupRespVO;
import com.trinasolar.integration.dao.*;
import com.trinasolar.integration.service.*;
import com.trinasolar.tasc.framework.common.exception.ServiceException;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.trinasolar.integration.util.ProjectConfigConstants.*;


@Service
@Validated
@Slf4j
public class ProjectServiceImpl implements ProjectService {

    @Resource
    private DevOpsProjectService devOpsProjectService;

    @Resource
    private ProjectConfigService projectConfigService;

    @Resource
    private AppSystemInitializeService appSystemInitializeService;

    @Resource
    private GitService gitService;

    @Resource
    private AppSystemMapper appSystemMapper;

    @Resource
    private ApplicationProgramMapper programMapper;

    @Resource
    private UserAppMapper userAppMapper;

    @Resource
    private ISpaceProvider spaceProvider;

    @Resource
    private AppSyncLogMapper appSyncLogMapper;

    @Resource
    private UserService userService;

    @Resource
    private SCAService scaService;

    @Resource
    private AppService appService;

    @Resource
    private DataShareService dataShareService;

    @Override
    public Boolean syncDownStream(List<SystemSyncReqDTO> systemSyncReqDTOList, Boolean browser) {
        long startTime = System.currentTimeMillis();
        //获取当前用户信息
        User user = userService.getUser();
        //获取应用系统信息
        Map<Long, List<SystemSyncReqDTO>> systemIdMap = systemSyncReqDTOList.stream().collect(Collectors.groupingBy(SystemSyncReqDTO::getSystemId));
        List<AppSystem> appSystems = appSystemMapper.selectByIds(systemIdMap.keySet());
        Map<Long, AppSystem> appSystemMap = appSystems.stream()
                .collect(Collectors.toMap(AppSystem::getId, appSystem -> appSystem));
        //获取应用系统负责人
        Map<Long, List<UserApp>> systemAdminUserMap = getSystemAdminUserMap(appSystemMap.keySet());
        long costTime1 = System.currentTimeMillis() - startTime;
        log.info("查询应用系统耗时{}ms", costTime1);

        // 只检查需要同步到devops或gitlab的项目的存在性
        Set<Long> systemsNeedCheck = systemIdMap.entrySet().stream()
                .filter(entry -> {
                    List<String> nextSyncSystem = entry.getValue().stream()
                            .map(e -> e.getDownStreamSystem().toLowerCase())
                            .collect(Collectors.toList());
                    return nextSyncSystem.contains(RelationTypeConstant.DVEOPS) ||
                            nextSyncSystem.contains(RelationTypeConstant.GITLAB);
                })
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        // 初始化项目存在性检查结果
        final Map<String, DevOpsProjectRespVO> existProjectsMap;
        if (!systemsNeedCheck.isEmpty()) {
            Set<String> projectNamesToCheck = systemsNeedCheck.stream()
                    .map(systemId -> appSystemMap.get(systemId))
                    .filter(Objects::nonNull)
                    .map(AppSystem::getCnName)
                    .collect(Collectors.toSet());

            Map<String, DevOpsProjectRespVO> tempExistProjectsMap = batchGetExistProjects(projectNamesToCheck);
            double percentage = (double) systemsNeedCheck.size() / systemIdMap.size() * 100;
            log.info(String.format("批量检查%d个项目的存在性耗时%dms，占比总系统数的%.1f%%",
                    projectNamesToCheck.size(),
                    System.currentTimeMillis() - startTime,
                    percentage));
            existProjectsMap = tempExistProjectsMap;
        } else {
            log.info("无需检查项目存在性，没有需要同步到devops或gitlab的系统");
            existProjectsMap = new HashMap<>();
        }

        // 创建自定义线程池，避免使用默认的ForkJoinPool.commonPool()
        int threadPoolSize = Math.min(Runtime.getRuntime().availableProcessors() * 2, systemIdMap.size());
        Executor executor = Executors.newFixedThreadPool(threadPoolSize);
        log.info("创建线程池处理{}个系统，线程池大小:{}", systemIdMap.size(), threadPoolSize);

        long parallelStartTime = System.currentTimeMillis();
        // 并行处理每个系统
        List<CompletableFuture<Void>> futures = systemIdMap.keySet().stream()
                .map(systemId -> CompletableFuture.runAsync(() ->
                        processSingleSystem(systemId, systemIdMap, appSystemMap, systemAdminUserMap, existProjectsMap, user), executor))
                .collect(Collectors.toList());

        // 等待所有并行任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            long parallelEndTime = System.currentTimeMillis();
            long totalTime = parallelEndTime - startTime;
            long parallelTime = parallelEndTime - parallelStartTime;
            double speedup = parallelTime > 0 ? (double) systemIdMap.size() * 1000 / parallelTime : 0;
            log.info(String.format("并行处理性能统计 - 总耗时:%dms, 并行处理耗时:%dms, 处理效率:%.1f个/秒",
                    totalTime, parallelTime, speedup));
        } catch (Exception e) {
            log.error("并行处理系统时发生异常", e);
        } finally {
            // 关闭线程池
            if (executor instanceof java.util.concurrent.ExecutorService) {
                ((java.util.concurrent.ExecutorService) executor).shutdown();
            }
        }

        return true;
    }

    private void saveDevopsAppSystemInitInfo(Long systemId, DevOpsProjectRespVO respVO) {
        AppSystemInitialize devopsInfo = new AppSystemInitialize(systemId, respVO.getProjectId(), respVO.getProjectName(), RelationTypeConstant.DVEOPS, respVO.getOriginData());
        devopsInfo.setCreatedTime(LocalDateTime.now());
        devopsInfo.setUpdatedTime(LocalDateTime.now());
        appSystemInitializeService.save(devopsInfo);
    }

    @Async
    public void syncProgramDownstream(Long systemId, List<String> nextSyncSystem) {
        List<ApplicationProgram> programs = programMapper.selectList(new LambdaQueryWrapper<ApplicationProgram>().eq(ApplicationProgram::getApplicationId, systemId));
        if (CollUtil.isNotEmpty(programs)) {
            programs.forEach(program ->
                    nextSyncSystem.forEach(nextSys -> {
                        appService.createApp(program, new SystemSyncReqDTO(systemId, nextSys));
                    })
            );
        }
    }


    /**
     * 获取devops管理员
     *
     * @param systemId 应用系统ID
     * @return
     */
    @Override
    public List<String> getSystemAdmin(Long systemId) {
        List<UserApp> userApps = userAppMapper.getUserAppByAppId(systemId);
        return userApps.stream().map(UserApp::getUserCode).distinct().collect(Collectors.toList());
    }

    @Override
    public List<UserApp> getSystemAdminUser(Long systemId) {
        return userAppMapper.getUserAppByAppId(systemId);
    }

    /**
     * 初始化devops信息（添加CICD的插件，添加环境组信息，设置制品库的信息，关联所有的凭证，创建git group，应用系统更新git信息）
     *
     * @param respVO
     * @param systemId
     */
    @Async
    public void doDevOpsInit(DevOpsProjectRespVO respVO, Long systemId) {
        //添加CICD的插件
        devOpsProjectService.addCicdPlugins(respVO.getProjectId());
        //添加环境组信息
        Map<String, String> envs = devOpsProjectService.addEnvGroup(respVO.getProjectId());
        projectConfigService.addProjectEnvConfigs(envs, systemId);
        //设置制品库的信息
        devOpsProjectService.enableDockerImgRepoPublic(respVO.getProjectId());
        //关联所有的凭证
        devOpsProjectService.relatedAllCredentials(respVO.getProjectId());
    }

    /**
     * 初始化gitlab信息:创建git group,更新AppSystemInitialize同步信息
     *
     * @param appSystem
     */
    @Async
    public void doGitInit(AppSystem appSystem) {
        //创建git group
        String namespace = appSystem.getEnSimpleName();
        String businessDomain = appSystem.getBusinessDomain();
        GitGroupRespVO gitGroup = gitService.createGitGroup(namespace, businessDomain);
        List<String> addUserNames = getSystemAdmin(appSystem.getId());
        gitService.addUserToGitGroup(businessDomain, namespace, addUserNames, GitLabAccessLevel.OWNER);
        //应用系统更新git信息
        AppSystemInitialize devopsInfo = new AppSystemInitialize(appSystem.getId(), String.valueOf(gitGroup.getId()), gitGroup.getName(), RelationTypeConstant.GITLAB, JSONUtil.toJsonStr(gitGroup));
        devopsInfo.setCreatedTime(LocalDateTime.now());
        devopsInfo.setUpdatedTime(LocalDateTime.now());
        appSystemInitializeService.save(devopsInfo);
    }

    public void doScaInit(AppSystem appSystem, List<UserApp> userApps) {
        List<Long> userIds = userApps.stream().map(UserApp::getUserId).collect(Collectors.toList());
        //应用系统更新sca信息
        AppSystemInitialize one = appSystemInitializeService.getOne(new LambdaQueryWrapper<AppSystemInitialize>()
                .eq(AppSystemInitialize::getAppId, appSystem.getId())
                .eq(AppSystemInitialize::getRelationType, RelationTypeConstant.SCA));
        if (Objects.nonNull(one)) {
            if (Objects.nonNull(one.getOriginData())) {
                JSONObject originData = JSON.parseObject(one.getOriginData());
                if (JSON.toJSONString(userIds).equals(originData.getString("userIds"))) {
                    log.info("sca项目已存在，且成员未发生变化，无需新增或更新，projectName:{}", appSystem.getCnName());
                    return;
                }
            } else {
                log.info("sca项目成员发生变化，开始更新，projectName:{}", appSystem.getCnName());
                scaService.updateProjects(Long.valueOf(one.getRelationId()), appSystem.getCnName(), userApps);
                JSONObject originDataUpdate = new JSONObject();
                originDataUpdate.put("originData", one.getRelationId());
                originDataUpdate.put("userIds", userIds);
                appSystemInitializeService.updateById(one);
                return;
            }
        }
        //新增sca项目
        Integer projectId = scaService.createProjects(appSystem.getCnName(), userApps);
        if (Objects.isNull(projectId)) {
            log.error("创建sca项目失败，projectName:{}", appSystem.getCnName());
            return;
        }
        JSONObject originData = new JSONObject();
        originData.put("originData", projectId);
        //sca项目成员
        originData.put("userIds", userIds);
        AppSystemInitialize devopsInfo = new AppSystemInitialize(appSystem.getId(), String.valueOf(projectId),
                appSystem.getCnName(), RelationTypeConstant.SCA, originData.toJSONString());
        devopsInfo.setCreatedTime(LocalDateTime.now());
        devopsInfo.setUpdatedTime(LocalDateTime.now());
        appSystemInitializeService.save(devopsInfo);
    }


    @Override
    public CommonResult<Long> createKnowlege(Long systemId) {
        AppSystem appSystem = appSystemMapper.selectById(systemId);
        if (appSystem == null) {
            log.error("创建知识库失败，应用系统不存在，systemId:{}", systemId);
            return CommonResult.error(ErrorCodeConstants.APP_NOT_EXISTS);
        }
        return createKnowlege(appSystem);
    }

    @Async
    public CommonResult<Long> createKnowlege(AppSystem appSystem) {
        SpaceDTO spaceDTO = new SpaceDTO();
        spaceDTO.setName(appSystem.getCnName());
        spaceDTO.setBucketName(appSystem.getBusinessDomain());
        spaceDTO.setDescription(appSystem.getEnName());
        spaceDTO.setProjectId(appSystem.getId());
        spaceDTO.setSpaceType(1);
        CommonResult<Long> longCommonResult = spaceProvider.create(spaceDTO);
        log.info("createKnowlege space result:{}", longCommonResult);
        return longCommonResult;
    }

    /**
     * 记录同步日志
     */
    private void recordAppSyncLog(Long systemId, AppSystem appSystem, User user, String downStreamName, String syncStatus) {
        AppSyncLog appSyncLog = new AppSyncLog();
        appSyncLog.setAppId(systemId);
        appSyncLog.setAppName(appSystem.getCnName());
        appSyncLog.setDownStreamName(downStreamName);
        appSyncLog.setCreatorId(Long.valueOf(user.getId()));
        appSyncLog.setCreatorName(user.getUserRealname());
        appSyncLog.setCreatedTime(LocalDateTime.now());
        appSyncLog.setSyncStatus(syncStatus);
        appSyncLogMapper.insert(appSyncLog);
    }

    private Map<Long, List<UserApp>> getSystemAdminUserMap(Set<Long> systemIds) {
        ArrayList<Long> objects = new ArrayList<>();
        objects.addAll(systemIds);
        List<UserApp> list = userAppMapper.selectUserByAppIds(objects);
        if (CollUtil.isEmpty(list)) {
            return Maps.newHashMap();
        }
        Map<Long, List<UserApp>> collect = list.stream().collect(Collectors.groupingBy(UserApp::getAppId));
        return collect;
    }

    /**
     * 批量检查项目是否存在，避免重复网络调用
     * 根据实际需要检查的项目数量动态调整并发度
     */
    private Map<String, DevOpsProjectRespVO> batchGetExistProjects(Set<String> projectNames) {
        if (projectNames.isEmpty()) {
            return new HashMap<>();
        }

        // 根据项目数量动态调整并发度，控制网络调用压力
        int maxConcurrency = Math.min(Math.max(projectNames.size() / 5 + 1, 1),
                Math.min(Runtime.getRuntime().availableProcessors() * 2, 20));

        log.debug("批量检查项目存在性，项目数量:{}, 并发度:{}", projectNames.size(), maxConcurrency);

        Map<String, DevOpsProjectRespVO> result = projectNames.stream()
                .collect(Collectors.groupingBy(projectName -> Math.abs(projectName.hashCode()) % maxConcurrency))
                .values()
                .parallelStream()
                .flatMap(partition -> partition.stream()
                        .map(projectName -> {
                            try {
                                DevOpsProjectRespVO existProject = devOpsProjectService.getExistProject(projectName);
                                return Objects.nonNull(existProject) && Objects.nonNull(existProject.getProjectId())
                                        ? Map.entry(projectName, existProject)
                                        : null;
                            } catch (Exception e) {
                                log.warn("检查项目{}存在性时发生异常:{}", projectName, e.getMessage());
                                return null;
                            }
                        })
                        .filter(Objects::nonNull))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        //devops产品存在，但数据没有初始化配置的要进行初始化配置
        //1.过滤存在的项目 2.检查数据库是否初始化 3.数据库未初始化的，要初始化
        //todo
        result.forEach((k, v) -> {
            if (Objects.nonNull(v)) {
               // projectConfigService.initCheck()
            }
        });
        return result;
    }

    /**
     * 并行处理单个系统
     */
    private void processSingleSystem(Long systemId, Map<Long, List<SystemSyncReqDTO>> systemIdMap,
                                     Map<Long, AppSystem> appSystemMap,
                                     Map<Long, List<UserApp>> systemAdminUserMap,
                                     Map<String, DevOpsProjectRespVO> existProjectsMap,
                                     User user) {
        List<String> nextSyncSystem = systemIdMap.get(systemId).stream()
                .map(e -> e.getDownStreamSystem().toLowerCase())
                .collect(Collectors.toList());
        AppSystem appSystem = appSystemMap.get(systemId);
        if (appSystem == null) {
            log.error("同步下游系统失败，应用系统不存在，systemId:{}", systemId);
            return;
        }

        //下游系统业务域使用小写
        appSystem.setBusinessDomain(appSystem.getBusinessDomain().toLowerCase());
        //查询应用系统负责人
        List<UserApp> systemAdminUser = systemAdminUserMap.get(systemId);
        log.info("应用系统负责人:{}", JSON.toJSONString(systemAdminUser));
        String syncStatus = "成功";

        try {
            // 关联devops系统：创建devops产品，并初始化配置
            if (nextSyncSystem.contains(RelationTypeConstant.DVEOPS)) {
                processDevOpsSync(appSystem, systemAdminUser, existProjectsMap, user, systemId, syncStatus);
            }

            // 关联gitlab：创建gitlab组
            if (nextSyncSystem.contains(RelationTypeConstant.GITLAB)) {
                processGitLabSync(appSystem, existProjectsMap, user, systemId, syncStatus);
            }

            //关联sca:初始化sca项目信息
            if (nextSyncSystem.contains(RelationTypeConstant.SCA)) {
                processScaSync(appSystem, systemAdminUser, user, systemId, syncStatus);
            }

            //CMDB等数据需要推送至kafka
            dataShareService.pushAppSystemToKafka(appSystem, nextSyncSystem, user.getUserRealname(), systemAdminUser);

            //同步应用程序到下游系统
            syncProgramDownstream(systemId, nextSyncSystem);
        } catch (Exception e) {
            log.error("处理系统{}时发生异常", appSystem.getCnName(), e);
        }
    }

    /**
     * 处理DevOps同步
     */
    private void processDevOpsSync(AppSystem appSystem, List<UserApp> systemAdminUser,
                                   Map<String, DevOpsProjectRespVO> existProjectsMap,
                                   User user, Long systemId, String syncStatus) {
        DevOpsProjectRespVO existProject = existProjectsMap.get(appSystem.getCnName());
        if (Objects.nonNull(existProject)) {
            log.info("devops项目已经存在，无需再次创建，项目名称:{}", appSystem.getCnName());
        } else {
            try {
                // 调用devops创建API，创建devops产品
                DevOpsProjectSaveReqVO reqVO = new DevOpsProjectSaveReqVO();
                reqVO.setProjectName(appSystem.getCnName());
                reqVO.setEnglishNameCustom(appSystem.getEnSimpleName());
                //应用系统负责人工号
                List<String> respUserCodesStr = systemAdminUser.stream().map(UserApp::getUserCode).distinct().collect(Collectors.toList());
                reqVO.setAdministrator(String.join(",", respUserCodesStr));
                DevOpsProjectRespVO respVO = devOpsProjectService.createProject(reqVO);
                if (Objects.isNull(respVO)) {
                    log.error("同步devops异常，创建devops项目失败，系统名称:{}", appSystem.getCnName());
                    throw new ServiceException(100110, "同步devops异常，创建devops项目失败:" + appSystem.getCnName());
                }
                // 保存项目（应用系统）配置信息
                projectConfigService.saveProjectConfig(respVO.getOriginData(), systemId, appSystem.getBusinessDomain());

                //保存应用系统同步信息（devops）
                saveDevopsAppSystemInitInfo(systemId, respVO);

                //初始化devops信息（添加CICD的插件，添加环境组信息，设置制品库的信息，关联所有的凭证，创建git group，应用系统更新git信息）
                doDevOpsInit(respVO, systemId);
            } catch (Exception e) {
                syncStatus = "失败";
                log.error("同步devops异常", e);
            } finally {
                //记录同步日志
                recordAppSyncLog(systemId, appSystem, user, RelationTypeConstant.DVEOPS, syncStatus);
            }
        }
    }

    /**
     * 处理GitLab同步
     */
    private void processGitLabSync(AppSystem appSystem, Map<String, DevOpsProjectRespVO> existProjectsMap,
                                   User user, Long systemId, String syncStatus) {
        DevOpsProjectRespVO existProject = existProjectsMap.get(appSystem.getCnName());
        if (Objects.nonNull(existProject)) {
            log.info("gitlab项目组已经存在，无需再次创建，项目名称:{}", appSystem.getCnName());
        } else {
            try {
                doGitInit(appSystem);
            } catch (Exception ex) {
                log.error("同步gitlab异常", ex);
                syncStatus = "失败";
            } finally {
                recordAppSyncLog(systemId, appSystem, user, RelationTypeConstant.GITLAB, syncStatus);
            }
        }
    }

    /**
     * 处理SCA同步
     */
    private void processScaSync(AppSystem appSystem, List<UserApp> systemAdminUser,
                                User user, Long systemId, String syncStatus) {
        try {
            doScaInit(appSystem, systemAdminUser);
        } catch (Exception ex) {
            syncStatus = "失败";
            log.error("同步sca异常", ex);
        } finally {
            recordAppSyncLog(systemId, appSystem, user, RelationTypeConstant.SCA, syncStatus);
        }
    }

}